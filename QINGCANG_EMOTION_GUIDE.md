# 擎苍2.0语音情感系统使用指南

## 📋 概述

本文档详细介绍Travel With应用中集成的擎苍2.0语音情感系统的配置、使用方法和最佳实践。

**模型版本**: BV701_V2_streaming  
**集成状态**: 配置完成，暂未实现情感功能  
**支持情感**: 10种情感音色  

## 🎭 支持的情感音色

### 1. 旁白类情感
| 情感音色 | 标识符 | 适用场景 | 推荐使用时机 |
|:--------:|:------:|:--------:|:------------:|
| 旁白-舒缓 | `narrator` | 平静叙述、介绍说明 | 地点介绍、旅行攻略、历史文化讲解 |
| 旁白-沉浸 | `narrator_immersive` | 深度沉浸体验、故事讲述 | 深度文化体验、历史故事、沉浸式导览 |

### 2. 基础情感
| 情感音色 | 标识符 | 适用场景 | 推荐使用时机 |
|:--------:|:------:|:--------:|:------------:|
| 平和 | `peaceful` | 日常对话、平和交流 | 默认对话音色，日常聊天交流 |
| 开心 | `happy` | 喜悦分享、兴奋表达 | 发现美景、分享快乐、庆祝时刻 |
| 悲伤 | `sad` | 难过安慰、失落表达 | 安慰用户、表达遗憾、同情理解 |

### 3. 强烈情感
| 情感音色 | 标识符 | 适用场景 | 推荐使用时机 |
|:--------:|:------:|:--------:|:------------:|
| 生气 | `angry` | 愤怒表达、不满情绪 | 表达不满、强调重要性、警告提醒 |
| 害怕 | `scare` | 恐惧表达、紧张情绪 | 危险提醒、紧急情况、安全警告 |
| 厌恶 | `hate` | 厌恶表达、反感情绪 | 负面评价、不推荐场所、强烈反对 |
| 惊讶 | `surprise` | 惊讶表达、意外情绪 | 意外发现、惊喜分享、突发情况 |
| 哭腔 | `tear` | 哭泣表达、深度悲伤 | 深度安慰、情感共鸣、感动时刻 |

## 🔧 技术实现

### 核心枚举定义
```swift
enum QingCangEmotion: String, CaseIterable {
    case narrator = "narrator"
    case narratorImmersive = "narrator_immersive"
    case peaceful = "peaceful"  // 默认音色
    case happy = "happy"
    case sad = "sad"
    case angry = "angry"
    case scare = "scare"
    case hate = "hate"
    case surprise = "surprise"
    case tear = "tear"
}
```

### 主要接口方法

#### 1. 设置情感音色
```swift
// 手动设置当前情感
ttsService.setEmotion(.happy)

// 合成时指定情感
ttsService.synthesizeAndPlay(text: "今天天气真好！", emotion: .happy)
```

#### 2. 智能情感推荐
```swift
// 基于文本内容推荐情感
let recommendedEmotion = ttsService.recommendEmotion(for: "今天真是太开心了！")
// 返回: .happy
```

#### 3. 状态查询
```swift
// 获取当前情感
let currentEmotion = ttsService.getCurrentEmotion()

// 获取所有支持的情感
let allEmotions = ttsService.getSupportedEmotions()
```

## 🎯 使用最佳实践

### 旅行场景应用建议

#### 景点介绍场景
- **推荐情感**: `narrator` (旁白-舒缓)
- **使用时机**: 介绍景点历史、文化背景、建筑特色
- **示例**: "这座古城建于明朝，有着600多年的历史..."

#### 美食分享场景
- **推荐情感**: `happy` (开心)
- **使用时机**: 推荐美食、分享用餐体验
- **示例**: "这家餐厅的小笼包真的太好吃了！"

#### 安全提醒场景
- **推荐情感**: `scare` (害怕) 或 `angry` (生气)
- **使用时机**: 重要安全提醒、紧急情况警告
- **示例**: "注意！前方道路施工，请绕行！"

#### 天气变化场景
- **推荐情感**: `surprise` (惊讶) 或 `sad` (悲伤)
- **使用时机**: 突然的天气变化、行程受影响
- **示例**: "哎呀，突然下雨了，我们先找个地方避雨吧。"

### 情感切换策略

#### 自然过渡
- 避免情感音色的突然切换
- 在对话的自然停顿点切换情感
- 保持情感与内容的一致性

#### 上下文感知
- 根据对话历史选择合适的情感
- 考虑用户当前的情绪状态
- 结合旅行场景的特殊需求

## 🚀 未来扩展计划

### 智能情感分析
- **AI情感识别**: 基于深度学习的文本情感分析
- **上下文理解**: 结合对话历史的情感状态推理
- **用户偏好学习**: 记住用户的情感偏好设置

### 高级情感控制
- **情感强度调节**: 支持情感强度的细粒度控制
- **混合情感表达**: 支持多种情感的混合表达
- **动态情感变化**: 在单次语音中实现情感的渐变

### 场景化预设
- **旅行场景模板**: 预设不同旅行场景的情感组合
- **个性化配置**: 用户自定义情感偏好和场景设置
- **智能推荐系统**: 基于用户行为的情感推荐优化

## 📝 开发注意事项

### 代码规范
- 所有情感相关代码都有详细的中文注释
- 使用统一的命名规范和代码结构
- 保持接口的向后兼容性

### 性能考虑
- 情感切换不应影响TTS合成速度
- 缓存常用情感配置，减少计算开销
- 优化情感推荐算法的执行效率

### 用户体验
- 提供清晰的情感状态反馈
- 支持用户手动覆盖智能推荐
- 确保情感表达的自然性和一致性

## 🔍 调试和测试

### 日志输出
系统会在控制台输出详细的情感相关日志：
```
🎭 切换情感音色: 开心 (happy)
📝 适用场景: 喜悦分享、兴奋表达
🎤 开始TTS合成: 今天天气真好！
🎭 当前情感: 开心 (happy)
🤖 擎苍2.0模型: BV701_V2_streaming
```

### 测试建议
- 测试所有10种情感音色的切换
- 验证智能推荐功能的准确性
- 检查情感状态的持久性和一致性
- 测试异常情况下的情感回退机制

---

**文档版本**: v1.0  
**最后更新**: 2025/8/2  
**维护者**: Travel With开发团队
